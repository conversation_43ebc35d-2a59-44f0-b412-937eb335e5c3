from rest_framework import serializers

from store.models import (
    AboutPage<PERSON>ontent,
    HeroSlide,
    HeroSlideImage,
    Organization,
    Testimonial,
    UserAccount,
)


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model"""

    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "logo",
            "location",
            "phone",
            "email",
            "mapping_url",
            "instagram",
            "facebook",
            "youtube",
            "tiktok",
        ]
        read_only_fields = ["id"]

    def validate_phone(self, value):
        """Custom validation for phone number"""
        if value and len(value) != 10:
            raise serializers.ValidationError("Phone number must be exactly 10 digits")
        if value and not value.isdigit():
            raise serializers.ValidationError("Phone number must contain only digits")
        return value


class HeroSlideImageSerializer(serializers.ModelSerializer):
    """Serializer for HeroSlideImage model"""

    class Meta:
        model = HeroSlideImage
        fields = ["id", "image", "order"]
        read_only_fields = ["id"]


class HeroSlideSerializer(serializers.ModelSerializer):
    """Serializer for HeroSlide model"""

    images = HeroSlideImageSerializer(many=True, read_only=True)

    class Meta:
        model = HeroSlide
        fields = [
            "id",
            "title",
            "description",
            "client",
            "service",
            "experience",
            "images",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class HeroSlideCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating HeroSlide with images"""

    images_data = serializers.ListField(
        child=serializers.ImageField(), write_only=True, required=False
    )

    class Meta:
        model = HeroSlide
        fields = [
            "id",
            "title",
            "description",
            "client",
            "service",
            "experience",
            "images_data",
        ]
        read_only_fields = ["id"]

    def create(self, validated_data):
        images_data = validated_data.pop("images_data", [])
        slide = HeroSlide.objects.create(**validated_data)

        for index, image_data in enumerate(images_data):
            HeroSlideImage.objects.create(slide=slide, image=image_data, order=index)

        return slide


class AboutPageContentSerializer(serializers.ModelSerializer):
    """Serializer for AboutPageContent model"""

    content_type_display = serializers.CharField(
        source="get_content_type_display", read_only=True
    )

    class Meta:
        model = AboutPageContent
        fields = [
            "id",
            "content_type",
            "content_type_display",
            "title",
            "content",
            "subtitle",
            "image",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def validate_content_type(self, value):
        """Ensure content_type is unique when creating"""
        if self.instance is None:  # Creating new instance
            if AboutPageContent.objects.filter(content_type=value).exists():
                raise serializers.ValidationError(
                    f"Content type '{
                        value
                    }' already exists. Each content type must be unique."
                )
        return value


class TestimonialUserSerializer(serializers.ModelSerializer):
    """Nested serializer for user information in testimonials"""

    full_name = serializers.CharField(source="get_full_name", read_only=True)

    class Meta:
        model = UserAccount
        fields = ["id", "first_name", "last_name", "full_name", "email"]
        read_only_fields = ["id", "first_name", "last_name", "full_name", "email"]


class TestimonialSerializer(serializers.ModelSerializer):
    """Serializer for Testimonial model"""

    user = TestimonialUserSerializer(read_only=True)
    rating_display = serializers.CharField(source="get_rating_display", read_only=True)

    class Meta:
        model = Testimonial
        fields = [
            "id",
            "user",
            "is_featured",
            "rating",
            "rating_display",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "user", "created_at", "updated_at"]


class TestimonialCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating testimonials"""

    class Meta:
        model = Testimonial
        fields = ["rating"]

    def create(self, validated_data):
        # Get user from request context
        user = self.context["request"].user
        validated_data["user"] = user
        return super().create(validated_data)

    def validate_rating(self, value):
        """Validate rating is between 1 and 5"""
        if value < 1 or value > 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


# List serializers for optimized list views
class OrganizationListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Organization list view"""

    class Meta:
        model = Organization
        fields = ["id", "name", "location", "phone", "email"]


class HeroSlideListSerializer(serializers.ModelSerializer):
    """Optimized serializer for HeroSlide list view"""

    images_count = serializers.SerializerMethodField()

    class Meta:
        model = HeroSlide
        fields = ["id", "title", "description", "images_count", "created_at"]

    def get_images_count(self, obj):
        return obj.images.count()


class AboutPageContentListSerializer(serializers.ModelSerializer):
    """Optimized serializer for AboutPageContent list view"""

    content_type_display = serializers.CharField(
        source="get_content_type_display", read_only=True
    )

    class Meta:
        model = AboutPageContent
        fields = ["id", "content_type", "content_type_display", "title", "is_active"]


class TestimonialListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Testimonial list view"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)

    class Meta:
        model = Testimonial
        fields = ["id", "user_name", "rating", "is_featured", "created_at"]
