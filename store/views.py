from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from store.models import (
    AboutPageContent,
    HeroSlide,
    HeroSlideImage,
    Organization,
    Testimonial,
)
from store.serializers import (
    AboutPageContentListSerializer,
    AboutPageContentSerializer,
    HeroSlideCreateSerializer,
    HeroSlideImageSerializer,
    HeroSlideListSerializer,
    HeroSlideSerializer,
    OrganizationListSerializer,
    OrganizationSerializer,
    TestimonialCreateSerializer,
    TestimonialListSerializer,
    TestimonialSerializer,
)


class OrganizationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Organization model
    Provides CRUD operations for organization information
    """

    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["name", "location", "email"]
    ordering_fields = ["name", "location"]
    ordering = ["name"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return OrganizationListSerializer
        return OrganizationSerializer

    def get_permissions(self):
        """
        Instantiate and return the list of permissions required for this view.
        Only staff users can create, update, or delete organizations.
        """
        if self.action in ["create", "update", "partial_update", "destroy"]:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=["get"])
    def active(self, request):
        """Get the first active organization (main organization info)"""
        try:
            org = self.queryset.first()
            if org:
                serializer = self.get_serializer(org)
                return Response(serializer.data)
            return Response(
                {"detail": "No organization found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class HeroSlideViewSet(viewsets.ModelViewSet):
    """
    ViewSet for HeroSlide model
    Provides CRUD operations for hero slides
    """

    queryset = HeroSlide.objects.prefetch_related("images").all()
    serializer_class = HeroSlideSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["title", "description"]
    ordering_fields = ["created_at", "updated_at", "title"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return HeroSlideListSerializer
        elif self.action == "create":
            return HeroSlideCreateSerializer
        return HeroSlideSerializer

    def get_permissions(self):
        """Only staff users can create, update, or delete slides"""
        if self.action in ["create", "update", "partial_update", "destroy"]:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=["post"], permission_classes=[permissions.IsAdminUser])
    def add_image(self, request, pk=None):
        """Add an image to a specific hero slide"""
        slide = self.get_object()
        serializer = HeroSlideImageSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save(slide=slide)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(
        detail=True, methods=["delete"], permission_classes=[permissions.IsAdminUser]
    )
    def remove_image(self, request, pk=None):
        """Remove an image from a hero slide"""
        slide = self.get_object()
        image_id = request.data.get("image_id")

        if not image_id:
            return Response(
                {"error": "image_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            image = slide.images.get(id=image_id)
            image.delete()
            return Response(
                {"message": "Image deleted successfully"},
                status=status.HTTP_204_NO_CONTENT,
            )
        except HeroSlideImage.DoesNotExist:
            return Response(
                {"error": "Image not found"}, status=status.HTTP_404_NOT_FOUND
            )


class HeroSlideImageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for HeroSlideImage model
    Provides CRUD operations for hero slide images
    """

    queryset = HeroSlideImage.objects.select_related("slide").all()
    serializer_class = HeroSlideImageSerializer
    permission_classes = [permissions.IsAdminUser]
    filter_backends = [OrderingFilter]
    ordering_fields = ["order", "slide"]
    ordering = ["slide", "order"]

    def get_queryset(self):
        """Filter images by slide if slide_id is provided"""
        queryset = super().get_queryset()
        slide_id = self.request.query_params.get("slide_id")
        if slide_id:
            queryset = queryset.filter(slide_id=slide_id)
        return queryset


class AboutPageContentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for AboutPageContent model
    Provides CRUD operations for about page content
    """

    queryset = AboutPageContent.objects.all()
    serializer_class = AboutPageContentSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ["title", "content", "subtitle"]
    ordering_fields = ["content_type", "title", "created_at", "updated_at"]
    ordering = ["content_type"]
    filterset_fields = ["content_type", "is_active"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return AboutPageContentListSerializer
        return AboutPageContentSerializer

    def get_permissions(self):
        """Only staff users can create, update, or delete content"""
        if self.action in ["create", "update", "partial_update", "destroy"]:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=["get"])
    def active(self, request):
        """Get all active content"""
        active_content = self.queryset.filter(is_active=True)
        serializer = self.get_serializer(active_content, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_type(self, request):
        """Get content by type"""
        content_type = request.query_params.get("type")
        if not content_type:
            return Response(
                {"error": "type parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            content = self.queryset.get(content_type=content_type, is_active=True)
            serializer = self.get_serializer(content)
            return Response(serializer.data)
        except AboutPageContent.DoesNotExist:
            return Response(
                {"error": f'Content type "{content_type}" not found'},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"], permission_classes=[permissions.IsAdminUser])
    def toggle_active(self, request, pk=None):
        """Toggle the active status of content"""
        content = self.get_object()
        content.is_active = not content.is_active
        content.save()
        serializer = self.get_serializer(content)
        return Response(serializer.data)


class TestimonialViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Testimonial model
    Provides CRUD operations for testimonials
    """

    queryset = Testimonial.objects.select_related("user").all()
    serializer_class = TestimonialSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ["user__first_name", "user__last_name", "user__email"]
    ordering_fields = ["rating", "created_at", "updated_at"]
    ordering = ["-created_at"]
    filterset_fields = ["rating", "is_featured"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return TestimonialListSerializer
        elif self.action == "create":
            return TestimonialCreateSerializer
        return TestimonialSerializer

    def get_permissions(self):
        """
        Permission logic:
        - Anyone can view testimonials
        - Authenticated users can create testimonials
        - Only staff can feature testimonials
        - Users can only update/delete their own testimonials
        """
        if self.action in ["list", "retrieve"]:
            permission_classes = [permissions.AllowAny]
        elif self.action == "create":
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ["approve", "feature", "bulk_approve"]:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filter queryset based on user permissions and action
        """
        queryset = super().get_queryset()

        # For update/destroy actions, users can only access their own testimonials
        if self.action in ["update", "partial_update", "destroy"]:
            if not self.request.user.is_staff:
                queryset = queryset.filter(user=self.request.user)

        return queryset

    @action(detail=False, methods=["get"])
    def featured(self, request):
        """Get featured testimonials"""
        featured_testimonials = self.queryset.filter(is_featured=True)
        serializer = self.get_serializer(featured_testimonials, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_rating(self, request):
        """Get testimonials filtered by rating"""
        rating = request.query_params.get("rating")
        if not rating:
            return Response(
                {"error": "rating parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            rating = int(rating)
            if rating < 1 or rating > 5:
                raise ValueError
        except ValueError:
            return Response(
                {"error": "rating must be an integer between 1 and 5"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        testimonials = self.queryset.filter(rating=rating)
        serializer = self.get_serializer(testimonials, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"], permission_classes=[permissions.IsAdminUser])
    def feature(self, request, pk=None):
        """Toggle featured status of a testimonial"""
        testimonial = self.get_object()
        testimonial.is_featured = not testimonial.is_featured
        testimonial.save()
        serializer = self.get_serializer(testimonial)
        return Response(serializer.data)

    @action(
        detail=False, methods=["get"], permission_classes=[permissions.IsAuthenticated]
    )
    def my_testimonials(self, request):
        """Get current user's testimonials"""
        user_testimonials = self.queryset.filter(user=request.user)
        serializer = self.get_serializer(user_testimonials, many=True)
        return Response(serializer.data)
