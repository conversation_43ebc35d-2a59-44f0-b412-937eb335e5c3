from django.apps import AppConfig


class StoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'store'
    verbose_name = 'Store Management'

    def ready(self):
        # Apply custom model names when app is ready
        from store.models import Organization, HeroSlide, HeroSlideImage, AboutPageContent, Testimonial

        Organization._meta.verbose_name = "Store Information"
        Organization._meta.verbose_name_plural = "Store Information"

        HeroSlide._meta.verbose_name = "Homepage Slide"
        HeroSlide._meta.verbose_name_plural = "Homepage Slides"

        HeroSlideImage._meta.verbose_name = "Slide Image"
        HeroSlideImage._meta.verbose_name_plural = "Slide Images"

        AboutPageContent._meta.verbose_name = "About Page Content"
        AboutPageContent._meta.verbose_name_plural = "About Page Contents"

        Testimonial._meta.verbose_name = "Customer Testimonial"
        Testimonial._meta.verbose_name_plural = "Customer Testimonials"
