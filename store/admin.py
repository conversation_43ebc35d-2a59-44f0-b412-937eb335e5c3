from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.db.models import Count
from django.urls import reverse
from django.utils.html import format_html

from store.models import (
    AboutPageContent,
    HeroSlide,
    HeroSlideImage,
    Organization,
    Testimonial,
)

# Model registration order for logical admin interface sequence:
# 1. Organization (Company foundation)
# 2. HeroSlide (Homepage content)
# 3. HeroSlideImage (Homepage images)
# 4. AboutPageContent (About page)
# 5. Testimonial (Customer feedback)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "logo_preview",
        "location",
        "phone",
        "email",
        "social_links_count",
    )
    search_fields = ("name", "location", "phone", "email")
    readonly_fields = ("logo_preview_large", "social_media_summary")
    list_per_page = 10
    verbose_name = "Organization"
    verbose_name_plural = "Organizations"

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("name", "logo", "logo_preview_large", "location")},
        ),
        ("Contact Information", {"fields": ("phone", "email", "mapping_url")}),
        (
            "Social Media",
            {
                "fields": (
                    "instagram",
                    "facebook",
                    "youtube",
                    "tiktok",
                    "social_media_summary",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def logo_preview(self, obj):
        if obj.logo:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 5px;" />',
                obj.logo.url,
            )
        return "No Logo"

    logo_preview.short_description = "Logo"

    def logo_preview_large(self, obj):
        if obj.logo:
            return format_html(
                '<img src="{}" width="150" height="150" style="border-radius: 10px;" />',
                obj.logo.url,
            )
        return "No Logo"

    logo_preview_large.short_description = "Organization Logo"

    def social_links_count(self, obj):
        links = [obj.instagram, obj.facebook, obj.youtube, obj.tiktok]
        active_links = sum(1 for link in links if link)
        if active_links > 0:
            return format_html(
                '<span style="color: green; font-weight: bold;">{}/4 platforms</span>',
                active_links,
            )
        return format_html('<span style="color: #999;">No social links</span>')

    social_links_count.short_description = "Social Media"

    def social_media_summary(self, obj):
        social_platforms = [
            ("Instagram", obj.instagram, "#E4405F"),
            ("Facebook", obj.facebook, "#1877F2"),
            ("YouTube", obj.youtube, "#FF0000"),
            ("TikTok", obj.tiktok, "#000000"),
        ]

        summary_html = '<div style="max-width: 400px;">'
        for platform, url, color in social_platforms:
            if url:
                summary_html += f'''
                    <div style="padding: 5px; border-bottom: 1px solid #eee;">
                        <span style="color: {color}; font-weight: bold;">{platform}:</span>
                        <a href="{url}" target="_blank" style="margin-left: 10px;">{url[:50]}...</a>
                    </div>
                '''
            else:
                summary_html += f"""
                    <div style="padding: 5px; border-bottom: 1px solid #eee; color: #999;">
                        <span style="font-weight: bold;">{platform}:</span> Not configured
                    </div>
                """
        summary_html += "</div>"
        return format_html(summary_html)

    social_media_summary.short_description = "Social Media Links"


class HeroSlideImageInline(admin.TabularInline):
    model = HeroSlideImage
    extra = 1
    fields = ("image", "order", "image_preview")
    readonly_fields = ("image_preview",)
    ordering = ("order",)

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="60" style="border-radius: 5px;" />',
                obj.image.url,
            )
        return "No Image"

    image_preview.short_description = "Preview"


@admin.register(HeroSlide)
class HeroSlideAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "description_preview",
        "stats_display",
        "image_count",
        "created_at",
        "updated_at",
    )
    search_fields = ("title", "description")
    readonly_fields = ("created_at", "updated_at", "slide_summary")
    ordering = ("-created_at",)
    list_per_page = 20
    verbose_name = "Hero Slide"
    verbose_name_plural = "Hero Slides"

    fieldsets = (
        ("Slide Content", {"fields": ("title", "description")}),
        (
            "Statistics",
            {
                "fields": ("client", "service", "experience"),
                "description": "Optional statistics to display on the slide",
            },
        ),
        ("Slide Summary", {"fields": ("slide_summary",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    inlines = [HeroSlideImageInline]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(_image_count=Count("images"))
        return queryset

    def description_preview(self, obj):
        if obj.description:
            preview = (
                obj.description[:50] + "..."
                if len(obj.description) > 50
                else obj.description
            )
            return format_html('<span title="{}">{}</span>', obj.description, preview)
        return format_html('<span style="color: #999;">No description</span>')

    description_preview.short_description = "Description"

    def stats_display(self, obj):
        stats = []
        if obj.client:
            stats.append(f"👥 {obj.client} clients")
        if obj.service:
            stats.append(f"🛍️ {obj.service} services")
        if obj.experience:
            stats.append(f"⭐ {obj.experience}+ years")

        if stats:
            return format_html(
                '<span style="font-size: 11px; color: #666;">{}</span>',
                " | ".join(stats),
            )
        return format_html('<span style="color: #999;">No stats</span>')

    stats_display.short_description = "Statistics"

    def image_count(self, obj):
        count = obj._image_count
        if count > 0:
            return format_html(
                '<span style="color: green; font-weight: bold;">{} images</span>', count
            )
        return format_html('<span style="color: red;">No images</span>')

    image_count.short_description = "Images"
    image_count.admin_order_field = "_image_count"

    def slide_summary(self, obj):
        images = obj.images.all()
        summary_html = f"""
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <h4>Slide Overview</h4>
                <div><strong>Title:</strong> {obj.title or "No title"}</div>
                <div><strong>Description:</strong> {obj.description[:100] + "..." if obj.description and len(obj.description) > 100 else obj.description or "No description"}</div>
                <div><strong>Images:</strong> {images.count()} attached</div>
                <div><strong>Statistics:</strong>
                    {f"Clients: {obj.client}, " if obj.client else ""}
                    {f"Services: {obj.service}, " if obj.service else ""}
                    {f"Experience: {obj.experience} years" if obj.experience else ""}
                </div>
            </div>
        """
        return format_html(summary_html)

    slide_summary.short_description = "Slide Summary"


class ContentTypeFilter(SimpleListFilter):
    title = "Content Type"
    parameter_name = "content_type"

    def lookups(self, request, model_admin):
        return AboutPageContent.CONTENT_TYPE_CHOICES

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(content_type=self.value())


@admin.register(AboutPageContent)
class AboutPageContentAdmin(admin.ModelAdmin):
    list_display = (
        "content_type_display",
        "title",
        "subtitle_preview",
        "image_preview",
        "status_display",
        "is_active",
        "created_at",
        "updated_at",
    )
    list_filter = (ContentTypeFilter, "is_active", "created_at")
    search_fields = ("title", "subtitle", "content")
    list_editable = ("is_active",)
    readonly_fields = (
        "created_at",
        "updated_at",
        "image_preview_large",
        "content_preview",
    )
    ordering = ("content_type",)
    list_per_page = 20

    fieldsets = (
        ("Content Information", {"fields": ("content_type", "title", "subtitle")}),
        (
            "Content Body",
            {"fields": ("content", "content_preview"), "classes": ("wide",)},
        ),
        (
            "Media",
            {"fields": ("image", "image_preview_large"), "classes": ("collapse",)},
        ),
        (
            "Status & Timestamps",
            {
                "fields": ("is_active", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["activate_content", "deactivate_content"]

    def content_type_display(self, obj):
        type_colors = {
            "story": "#007bff",
            "mission": "#28a745",
            "vision": "#6f42c1",
            "values": "#fd7e14",
            "header": "#20c997",
        }
        color = type_colors.get(obj.content_type, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold; padding: 3px 8px; border-radius: 3px; background: {}20;">{}</span>',
            color,
            color,
            obj.get_content_type_display(),
        )

    content_type_display.short_description = "Type"
    content_type_display.admin_order_field = "content_type"

    def subtitle_preview(self, obj):
        if obj.subtitle:
            preview = (
                obj.subtitle[:30] + "..." if len(obj.subtitle) > 30 else obj.subtitle
            )
            return format_html('<span title="{}">{}</span>', obj.subtitle, preview)
        return format_html('<span style="color: #999;">No subtitle</span>')

    subtitle_preview.short_description = "Subtitle"

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 5px;" />',
                obj.image.url,
            )
        return format_html('<span style="color: #999;">No image</span>')

    image_preview.short_description = "Image"

    def image_preview_large(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="200" height="200" style="border-radius: 10px;" />',
                obj.image.url,
            )
        return "No Image"

    image_preview_large.short_description = "Content Image"

    def status_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓ Active</span>')
        return format_html('<span style="color: red;">✗ Inactive</span>')

    status_display.short_description = "Status"
    status_display.admin_order_field = "is_active"

    def content_preview(self, obj):
        if obj.content:
            preview = (
                obj.content[:300] + "..." if len(obj.content) > 300 else obj.content
            )
            return format_html(
                '<div style="max-width: 500px; padding: 10px; background: #f8f9fa; border-radius: 5px; white-space: pre-wrap;">{}</div>',
                preview,
            )
        return "No content"

    content_preview.short_description = "Content Preview"

    def activate_content(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} content items activated.")

    activate_content.short_description = "Activate selected content"

    def deactivate_content(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} content items deactivated.")

    deactivate_content.short_description = "Deactivate selected content"


class TestimonialStatusFilter(SimpleListFilter):
    title = "Featured Status"
    parameter_name = "featured_status"

    def lookups(self, request, model_admin):
        return (
            ("featured", "Featured"),
            ("regular", "Regular"),
        )

    def queryset(self, request, queryset):
        if self.value() == "featured":
            return queryset.filter(is_featured=True)
        elif self.value() == "regular":
            return queryset.filter(is_featured=False)


class RatingFilter(SimpleListFilter):
    title = "Rating"
    parameter_name = "rating"

    def lookups(self, request, model_admin):
        return [(i, f"{i} Star{'s' if i > 1 else ''}") for i in range(1, 6)]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(rating=int(self.value()))


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = (
        "user_display",
        "rating_display",
        "featured_status",
        "is_featured",
        "created_at",
    )
    list_filter = (TestimonialStatusFilter, RatingFilter, "is_featured", "created_at")
    search_fields = ("user__email", "user__first_name", "user__last_name")
    list_editable = ("is_featured",)
    readonly_fields = ("created_at", "updated_at", "testimonial_summary")
    ordering = ("-created_at",)
    list_per_page = 25
    date_hierarchy = "created_at"
    verbose_name = "Customer Testimonial"
    verbose_name_plural = "Customer Testimonials"

    fieldsets = (
        ("Testimonial Information", {"fields": ("user", "rating")}),
        ("Status", {"fields": ("is_featured",)}),
        ("Summary", {"fields": ("testimonial_summary",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["feature_testimonials", "unfeature_testimonials"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")

    def user_display(self, obj):
        display_name = (
            f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
        )
        url = reverse("admin:account_useraccount_change", args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, display_name)

    user_display.short_description = "User"
    user_display.admin_order_field = "user__email"

    def rating_display(self, obj):
        stars = "★" * obj.rating + "☆" * (5 - obj.rating)
        color = "green" if obj.rating >= 4 else "orange" if obj.rating >= 3 else "red"
        return format_html(
            '<span style="color: {}; font-size: 16px;" title="{}/5">{}</span>',
            color,
            obj.rating,
            stars,
        )

    rating_display.short_description = "Rating"
    rating_display.admin_order_field = "rating"

    def featured_status(self, obj):
        if obj.is_featured:
            return format_html(
                '<span style="color: gold; font-weight: bold;">⭐ Featured</span>'
            )
        return format_html('<span style="color: #999;">Regular</span>')

    featured_status.short_description = "Featured"
    featured_status.admin_order_field = "is_featured"

    def testimonial_summary(self, obj):
        summary_html = f"""
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <h4>Testimonial Overview</h4>
                <div><strong>User:</strong> {obj.user.first_name} {obj.user.last_name} ({obj.user.email})</div>
                <div><strong>Rating:</strong> {"★" * obj.rating}{"☆" * (5 - obj.rating)} ({obj.rating}/5)</div>
                <div><strong>Status:</strong>
                    {"Featured" if obj.is_featured else "Regular"}
                </div>
                <div><strong>Created:</strong> {obj.created_at.strftime("%B %d, %Y at %I:%M %p")}</div>
            </div>
        """
        return format_html(summary_html)

    testimonial_summary.short_description = "Testimonial Summary"

    def feature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} testimonials featured.")

    feature_testimonials.short_description = "Feature selected testimonials"

    def unfeature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"{updated} testimonials unfeatured.")

    unfeature_testimonials.short_description = "Unfeature selected testimonials"


@admin.register(HeroSlideImage)
class HeroSlideImageAdmin(admin.ModelAdmin):
    list_display = ("slide_link", "image_preview", "order", "image_info")
    list_filter = ("slide",)
    search_fields = ("slide__title",)
    list_editable = ("order",)
    ordering = ("slide", "order")
    list_per_page = 30
    verbose_name = "Hero Slide Image"
    verbose_name_plural = "Hero Slide Images"

    def slide_link(self, obj):
        url = reverse("admin:store_heroslide_change", args=[obj.slide.pk])
        return format_html(
            '<a href="{}">{}</a>', url, obj.slide.title or f"Slide {obj.slide.id}"
        )

    slide_link.short_description = "Slide"
    slide_link.admin_order_field = "slide__title"

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="60" style="border-radius: 5px;" />',
                obj.image.url,
            )
        return "No Image"

    image_preview.short_description = "Preview"

    def image_info(self, obj):
        if obj.image:
            return format_html("<small>Order: {}</small>", obj.order)
        return "-"

    image_info.short_description = "Info"
