from django.contrib.auth import get_user_model
from django.db.models import Avg, Sum
from djoser.serializers import UserCreateSerializer
from rest_framework import serializers

User = get_user_model()


class UserCreateSerializer(UserCreateSerializer):
    class Meta(UserCreateSerializer.Meta):
        model = User
        fields = ("id", "email", "first_name", "last_name", "password")


class UserProfileSerializer(serializers.ModelSerializer):
    """Extended user profile serializer with additional information"""

    full_name = serializers.Char<PERSON>ield(source="get_full_name", read_only=True)
    is_google_user = serializers.SerializerMethodField()
    account_type = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "full_name",
            "is_google_user",
            "account_type",
            "is_active",
            "last_login",
        ]
        read_only_fields = ["id", "email", "is_active", "last_login"]

    def get_is_google_user(self, obj):
        """Check if user registered via Google OAuth"""
        return bool(obj.google_id)

    def get_account_type(self, obj):
        """Get user account type"""
        if obj.is_superuser:
            return "superuser"
        elif obj.is_staff:
            return "staff"
        elif obj.google_id:
            return "google"
        else:
            return "regular"


class UserActivitySerializer(serializers.ModelSerializer):
    """Serializer for user activity summary"""

    full_name = serializers.CharField(source="get_full_name", read_only=True)
    order_count = serializers.SerializerMethodField()
    total_spent = serializers.SerializerMethodField()
    cart_items_count = serializers.SerializerMethodField()
    reviews_count = serializers.SerializerMethodField()
    testimonials_count = serializers.SerializerMethodField()
    average_rating_given = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "full_name",
            "order_count",
            "total_spent",
            "cart_items_count",
            "reviews_count",
            "testimonials_count",
            "average_rating_given",
            "last_login",
        ]

    def get_order_count(self, obj):
        """Get total number of orders"""
        return getattr(obj, "_order_count", obj.order_set.count())

    def get_total_spent(self, obj):
        """Get total amount spent"""
        return getattr(
            obj,
            "_total_spent",
            obj.order_set.aggregate(total=Sum("grand_total"))["total"] or 0,
        )

    def get_cart_items_count(self, obj):
        """Get current cart items count"""
        return getattr(
            obj, "_cart_items", obj.cartitem_set.filter(is_active=True).count()
        )

    def get_reviews_count(self, obj):
        """Get total reviews written"""
        return getattr(obj, "_reviews_count", obj.reviewrating_set.count())

    def get_testimonials_count(self, obj):
        """Get total testimonials written"""
        return getattr(obj, "_testimonials_count", obj.testimonials.count())

    def get_average_rating_given(self, obj):
        """Get average rating given by user"""
        avg = getattr(
            obj,
            "_avg_rating_given",
            obj.reviewrating_set.aggregate(avg=Avg("rating"))["avg"],
        )
        return round(avg, 2) if avg else 0.0


class UserStatsSerializer(serializers.Serializer):
    """Serializer for detailed user statistics"""

    orders = serializers.DictField()
    reviews = serializers.DictField()
    cart = serializers.DictField()
    testimonials = serializers.DictField()


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile"""

    class Meta:
        model = User
        fields = ["first_name", "last_name"]

    def validate_first_name(self, value):
        """Validate first name"""
        if not value or not value.strip():
            raise serializers.ValidationError("First name cannot be empty")
        return value.strip()

    def validate_last_name(self, value):
        """Validate last name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Last name cannot be empty")
        return value.strip()
