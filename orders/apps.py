from django.apps import AppConfig


class OrdersConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'orders'
    verbose_name = 'Order Management'

    def ready(self):
        # Apply custom model names when app is ready
        from orders.models import Order, OrderProduct, Payment, PaymentMethod

        Order._meta.verbose_name = "Customer Order"
        Order._meta.verbose_name_plural = "Customer Orders"

        OrderProduct._meta.verbose_name = "Order Item"
        OrderProduct._meta.verbose_name_plural = "Order Items"

        Payment._meta.verbose_name = "Payment Record"
        Payment._meta.verbose_name_plural = "Payment Records"

        PaymentMethod._meta.verbose_name = "Payment Method"
        PaymentMethod._meta.verbose_name_plural = "Payment Methods"
