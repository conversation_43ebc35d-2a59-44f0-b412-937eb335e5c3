from rest_framework import serializers

from account.models import UserAccount
from carts.models import Cart, CartItem
from products.models import Product, Variation


class CartSerializer(serializers.ModelSerializer):
    """Serializer for Cart model"""

    items_count = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    total_quantity = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = [
            "id",
            "cart_id",
            "date_added",
            "items_count",
            "total_amount",
            "total_quantity",
        ]
        read_only_fields = ["id", "date_added"]

    def get_items_count(self, obj):
        """Get count of active items in cart"""
        return obj.cartitem_set.filter(is_active=True).count()

    def get_total_amount(self, obj):
        """Calculate total amount of all items in cart"""
        items = obj.cartitem_set.filter(is_active=True)
        return sum(item.sub_total() for item in items)

    def get_total_quantity(self, obj):
        """Get total quantity of all items in cart"""
        items = obj.cartitem_set.filter(is_active=True)
        return sum(item.quantity for item in items)


class CartCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Cart"""

    class Meta:
        model = Cart
        fields = ["cart_id"]

    def validate_cart_id(self, value):
        """Validate cart_id uniqueness"""
        if Cart.objects.filter(cart_id=value).exists():
            raise serializers.ValidationError("Cart with this ID already exists")
        return value


class CartListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Cart list view"""

    items_count = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = ["id", "cart_id", "date_added", "items_count", "total_amount"]

    def get_items_count(self, obj):
        """Get count of active items in cart"""
        return getattr(
            obj, "_items_count", obj.cartitem_set.filter(is_active=True).count()
        )

    def get_total_amount(self, obj):
        """Calculate total amount of all items in cart"""
        if hasattr(obj, "_total_amount"):
            return obj._total_amount
        items = obj.cartitem_set.filter(is_active=True)
        return sum(item.sub_total() for item in items)


# Nested serializers for related models
class ProductBasicSerializer(serializers.ModelSerializer):
    """Basic product info for cart items"""

    category_name = serializers.CharField(source="category.name", read_only=True)

    class Meta:
        model = Product
        fields = ["id", "name", "price", "image", "category_name", "in_stock"]


class VariationSerializer(serializers.ModelSerializer):
    """Serializer for Variation model"""

    class Meta:
        model = Variation
        fields = ["id", "variation_category", "variation_value", "is_active"]


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user info for cart items"""

    full_name = serializers.CharField(source="get_full_name", read_only=True)

    class Meta:
        model = UserAccount
        fields = ["id", "email", "first_name", "last_name", "full_name"]


class CartItemSerializer(serializers.ModelSerializer):
    """Serializer for CartItem model"""

    product = ProductBasicSerializer(read_only=True)
    variation = VariationSerializer(many=True, read_only=True)
    user = UserBasicSerializer(read_only=True)
    cart_id = serializers.CharField(source="cart.cart_id", read_only=True)
    sub_total = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = [
            "id",
            "user",
            "product",
            "variation",
            "cart_id",
            "quantity",
            "is_active",
            "sub_total",
        ]
        read_only_fields = ["id", "user", "cart"]

    def get_sub_total(self, obj):
        """Calculate subtotal for this cart item"""
        return obj.sub_total()

    def validate_quantity(self, value):
        """Validate quantity is positive"""
        if value < 1:
            raise serializers.ValidationError("Quantity must be at least 1")
        return value


class CartItemCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating CartItem"""

    product_id = serializers.IntegerField(write_only=True)
    variation_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        allow_empty=True,
    )

    class Meta:
        model = CartItem
        fields = ["product_id", "variation_ids", "quantity"]

    def validate_product_id(self, value):
        """Validate product exists and is active"""
        try:
            product = Product.objects.get(id=value)
            if not product.is_active:
                raise serializers.ValidationError("Product is not active")
            if not product.in_stock:
                raise serializers.ValidationError("Product is out of stock")
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def validate_variation_ids(self, value):
        """Validate variations exist and belong to the product"""
        if value:
            variations = Variation.objects.filter(id__in=value)
            if len(variations) != len(value):
                raise serializers.ValidationError("One or more variations do not exist")

            # Check if all variations are active
            inactive_variations = variations.filter(is_active=False)
            if inactive_variations.exists():
                raise serializers.ValidationError(
                    "One or more variations are not active"
                )

        return value

    def validate(self, attrs):
        """Cross-field validation"""
        product_id = attrs.get("product_id")
        variation_ids = attrs.get("variation_ids", [])

        if variation_ids:
            # Ensure all variations belong to the specified product
            product = Product.objects.get(id=product_id)
            variations = Variation.objects.filter(id__in=variation_ids)

            for variation in variations:
                if variation.product != product:
                    raise serializers.ValidationError(
                        f"Variation '{
                            variation.variation_value
                        }' does not belong to product '{product.name}'"
                    )

        return attrs

    def create(self, validated_data):
        """Create cart item with variations"""
        product_id = validated_data.pop("product_id")
        variation_ids = validated_data.pop("variation_ids", [])

        product = Product.objects.get(id=product_id)
        cart_item = CartItem.objects.create(product=product, **validated_data)

        if variation_ids:
            variations = Variation.objects.filter(id__in=variation_ids)
            cart_item.variation.set(variations)

        return cart_item


class CartItemListSerializer(serializers.ModelSerializer):
    """Optimized serializer for CartItem list view"""

    product_name = serializers.CharField(source="product.name", read_only=True)
    product_price = serializers.DecimalField(
        source="product.price", max_digits=10, decimal_places=2, read_only=True
    )
    product_image = serializers.ImageField(source="product.image", read_only=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    cart_id = serializers.CharField(source="cart.cart_id", read_only=True)
    sub_total = serializers.SerializerMethodField()
    variations_display = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = [
            "id",
            "product_name",
            "product_price",
            "product_image",
            "user_name",
            "cart_id",
            "quantity",
            "sub_total",
            "variations_display",
            "is_active",
        ]

    def get_sub_total(self, obj):
        """Calculate subtotal for this cart item"""
        return obj.sub_total()

    def get_variations_display(self, obj):
        """Get formatted variations display"""
        variations = obj.variation.filter(is_active=True)
        if variations.exists():
            return [f"{v.variation_category}: {v.variation_value}" for v in variations]
        return []
