from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response

from carts.models import Cart, CartItem
from carts.serializers import (
    CartCreateSerializer,
    CartItemCreateSerializer,
    CartItemListSerializer,
    CartItemSerializer,
    CartListSerializer,
    CartSerializer,
)


class CartViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Cart model
    Provides CRUD operations for shopping carts
    """

    queryset = Cart.objects.all()
    serializer_class = CartSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["cart_id"]
    ordering_fields = ["date_added"]
    ordering = ["-date_added"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return CartListSerializer
        elif self.action == "create":
            return CartCreateSerializer
        return CartSerializer

    def get_queryset(self):
        """Filter carts based on user permissions"""
        queryset = Cart.objects.prefetch_related(
            "cartitem_set__product", "cartitem_set__variation"
        )

        # If user is authenticated, they can see their own carts
        if self.request.user.is_authenticated:
            if self.request.user.is_staff:
                return queryset  # Staff can see all carts
            else:
                # Regular users can see carts with their items
                return queryset.filter(cartitem__user=self.request.user).distinct()

        # Anonymous users can see carts by cart_id (session-based)
        cart_id = self.request.session.get("cart_id")
        if cart_id:
            return queryset.filter(cart_id=cart_id)

        return queryset.none()

    @action(detail=True, methods=["get"])
    def items(self, request, pk=None):
        """Get all items in a specific cart"""
        cart = self.get_object()
        items = (
            CartItem.objects.filter(cart=cart, is_active=True)
            .select_related("product", "user")
            .prefetch_related("variation")
        )

        serializer = CartItemListSerializer(
            items, many=True, context={"request": request}
        )
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def total(self, request, pk=None):
        """Get cart total and summary"""
        cart = self.get_object()
        items = CartItem.objects.filter(cart=cart, is_active=True)

        total_amount = sum(item.sub_total() for item in items)
        total_items = items.count()
        total_quantity = sum(item.quantity for item in items)

        return Response(
            {
                "cart_id": cart.cart_id,
                "total_amount": total_amount,
                "total_items": total_items,
                "total_quantity": total_quantity,
                "items": CartItemListSerializer(
                    items, many=True, context={"request": request}
                ).data,
            }
        )


class CartItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for CartItem model
    Provides CRUD operations for cart items
    """

    queryset = CartItem.objects.select_related(
        "product", "user", "cart"
    ).prefetch_related("variation")
    serializer_class = CartItemSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = [
        "product__name",
        "user__email",
        "user__first_name",
        "user__last_name",
    ]
    ordering_fields = ["quantity", "cart__date_added"]
    ordering = ["-cart__date_added"]
    filterset_fields = ["is_active", "product__category", "user"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return CartItemListSerializer
        elif self.action == "create":
            return CartItemCreateSerializer
        return CartItemSerializer

    def get_queryset(self):
        """Filter cart items based on user permissions"""
        queryset = self.queryset

        if self.request.user.is_authenticated:
            if self.request.user.is_staff:
                return queryset  # Staff can see all cart items
            else:
                # Regular users can only see their own cart items
                return queryset.filter(user=self.request.user)

        # Anonymous users can see items from their session cart
        cart_id = self.request.session.get("cart_id")
        if cart_id:
            return queryset.filter(cart__cart_id=cart_id, user__isnull=True)

        return queryset.none()

    def perform_create(self, serializer):
        """Set user and cart when creating cart item"""
        cart_id = self.request.session.get("cart_id")
        cart = None

        if cart_id:
            cart = Cart.objects.filter(cart_id=cart_id).first()

        if not cart:
            # Create new cart if none exists
            import uuid

            cart_id = str(uuid.uuid4())
            cart = Cart.objects.create(cart_id=cart_id)
            self.request.session["cart_id"] = cart_id

        # Set user if authenticated
        user = self.request.user if self.request.user.is_authenticated else None

        serializer.save(user=user, cart=cart)

    @action(detail=True, methods=["post"])
    def update_quantity(self, request, pk=None):
        """Update quantity of a cart item"""
        cart_item = self.get_object()
        quantity = request.data.get("quantity")

        if not quantity or not isinstance(quantity, int) or quantity < 1:
            return Response(
                {"error": "Valid quantity is required (minimum 1)"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        cart_item.quantity = quantity
        cart_item.save()

        serializer = self.get_serializer(cart_item)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def toggle_active(self, request, pk=None):
        """Toggle active status of cart item"""
        cart_item = self.get_object()
        cart_item.is_active = not cart_item.is_active
        cart_item.save()

        serializer = self.get_serializer(cart_item)
        return Response(serializer.data)
