from django.apps import AppConfig


class CartsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'carts'
    verbose_name = 'Shopping Management'

    def ready(self):
        # Apply custom model names when app is ready
        from carts.models import Cart, CartItem

        Cart._meta.verbose_name = "Shopping Cart"
        Cart._meta.verbose_name_plural = "Shopping Carts"

        CartItem._meta.verbose_name = "Cart Item"
        CartItem._meta.verbose_name_plural = "Cart Items"
