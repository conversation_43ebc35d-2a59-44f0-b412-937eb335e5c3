JAZZMIN_SETTINGS = {
    "site_title": "Mah<PERSON>eau<PERSON>",
    "site_header": "MahBeauty",
    "site_brand": "Mah<PERSON><PERSON><PERSON>",
    "welcome_sign": "Welcome to MahBeauty Admin",
    "copyright": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "site_logo": "img/logo.png",
    "login_logo": "img/logo.png",

    # Menu ordering
    "order_with_respect_to": ["account", "store", "products", "orders", "carts"],

    # Custom links to control model ordering within apps
    "custom_links": {
        "store": [
            {
                "name": "Organization",
                "url": "admin:store_organization_changelist",
                "icon": "fas fa-building",
                "permissions": ["store.view_organization"]
            },
            {
                "name": "Hero Slides",
                "url": "admin:store_heroslide_changelist",
                "icon": "fas fa-images",
                "permissions": ["store.view_heroslide"]
            },
            {
                "name": "Hero Slide Images",
                "url": "admin:store_heroslideimage_changelist",
                "icon": "fas fa-image",
                "permissions": ["store.view_heroslideimage"]
            },
            {
                "name": "About Page Content",
                "url": "admin:store_aboutpagecontent_changelist",
                "icon": "fas fa-info-circle",
                "permissions": ["store.view_aboutpagecontent"]
            },
            {
                "name": "Testimonials",
                "url": "admin:store_testimonial_changelist",
                "icon": "fas fa-quote-left",
                "permissions": ["store.view_testimonial"]
            },
        ],
        "products": [
            {
                "name": "Product Categories",
                "url": "admin:products_productcategory_changelist",
                "icon": "fas fa-tags",
                "permissions": ["products.view_productcategory"]
            },
            {
                "name": "Products",
                "url": "admin:products_product_changelist",
                "icon": "fas fa-box",
                "permissions": ["products.view_product"]
            },
            {
                "name": "Product Variations",
                "url": "admin:products_variation_changelist",
                "icon": "fas fa-palette",
                "permissions": ["products.view_variation"]
            },
            {
                "name": "Product Reviews",
                "url": "admin:products_reviewrating_changelist",
                "icon": "fas fa-star",
                "permissions": ["products.view_reviewrating"]
            },
        ],
        "orders": [
            {
                "name": "Payment Methods",
                "url": "admin:orders_paymentmethod_changelist",
                "icon": "fas fa-credit-card",
                "permissions": ["orders.view_paymentmethod"]
            },
            {
                "name": "Payments",
                "url": "admin:orders_payment_changelist",
                "icon": "fas fa-money-bill",
                "permissions": ["orders.view_payment"]
            },
            {
                "name": "Customer Orders",
                "url": "admin:orders_order_changelist",
                "icon": "fas fa-receipt",
                "permissions": ["orders.view_order"]
            },
            {
                "name": "Order Products",
                "url": "admin:orders_orderproduct_changelist",
                "icon": "fas fa-list",
                "permissions": ["orders.view_orderproduct"]
            },
        ],
        "carts": [
            {
                "name": "Carts",
                "url": "admin:carts_cart_changelist",
                "icon": "fas fa-shopping-basket",
                "permissions": ["carts.view_cart"]
            },
            {
                "name": "Cart Items",
                "url": "admin:carts_cartitem_changelist",
                "icon": "fas fa-plus-square",
                "permissions": ["carts.view_cartitem"]
            },
        ],
        "account": [
            {
                "name": "User Accounts",
                "url": "admin:account_useraccount_changelist",
                "icon": "fas fa-user",
                "permissions": ["account.view_useraccount"]
            },
        ],
    },

    # Icons for apps and models
    "icons": {
        "account": "fas fa-users-cog",
        "account.UserAccount": "fas fa-user",
        "products": "fas fa-shopping-bag",
        "products.ProductCategory": "fas fa-tags",
        "products.Product": "fas fa-box",
        "products.Variation": "fas fa-palette",
        "products.ReviewRating": "fas fa-star",
        "orders": "fas fa-shopping-cart",
        "orders.Order": "fas fa-receipt",
        "orders.OrderProduct": "fas fa-list",
        "orders.Payment": "fas fa-credit-card",
        "carts": "fas fa-shopping-basket",
        "carts.Cart": "fas fa-shopping-basket",
        "carts.CartItem": "fas fa-plus-square",
        "store": "fas fa-store",
        "store.Organization": "fas fa-building",
        "store.HeroSlide": "fas fa-images",
        "store.HeroSlideImage": "fas fa-image",
        "store.AboutPageContent": "fas fa-info-circle",
        "store.Testimonial": "fas fa-quote-left",
    },

    # UI Settings
    "show_sidebar": True,
    "navigation_expanded": True,
    "changeform_format": "horizontal_tabs",
    "related_modal_active": False,
}

JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": False,
    "accent": "accent-primary",
    "navbar": "navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": False,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": False,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": False,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "default",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    }
}