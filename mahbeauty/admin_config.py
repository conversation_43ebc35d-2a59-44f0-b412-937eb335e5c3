"""
Custom admin configuration for MahBeauty project
This file contains model name overrides and admin customizations
"""

from django.contrib import admin
from django.apps import apps


class ModelNameOverride:
    """
    Class to handle model name overrides in Django admin
    """
    
    # Model name mapping organized by logical groups
    MODEL_DISPLAY_NAMES = {
        # User Management & Authentication
        "account.UserAccount": {
            "verbose_name": "Customer Account",
            "verbose_name_plural": "Customer Accounts",
            "group": "User Management"
        },
        
        # Beauty Products & Catalog Management
        "products.ProductCategory": {
            "verbose_name": "Product Category",
            "verbose_name_plural": "Product Categories", 
            "group": "Product Catalog"
        },
        "products.Product": {
            "verbose_name": "Beauty Product",
            "verbose_name_plural": "Beauty Products",
            "group": "Product Catalog"
        },
        "products.Variation": {
            "verbose_name": "Product Variation",
            "verbose_name_plural": "Product Variations",
            "group": "Product Catalog"
        },
        "products.ReviewRating": {
            "verbose_name": "Customer Review",
            "verbose_name_plural": "Customer Reviews",
            "group": "Product Catalog"
        },
        
        # Shopping Cart Management
        "carts.Cart": {
            "verbose_name": "Shopping Cart",
            "verbose_name_plural": "Shopping Carts",
            "group": "Shopping Management"
        },
        "carts.CartItem": {
            "verbose_name": "Cart Item",
            "verbose_name_plural": "Cart Items",
            "group": "Shopping Management"
        },
        
        # Order & Payment Management
        "orders.Order": {
            "verbose_name": "Customer Order",
            "verbose_name_plural": "Customer Orders",
            "group": "Order Management"
        },
        "orders.OrderProduct": {
            "verbose_name": "Order Item",
            "verbose_name_plural": "Order Items",
            "group": "Order Management"
        },
        "orders.Payment": {
            "verbose_name": "Payment Record",
            "verbose_name_plural": "Payment Records",
            "group": "Order Management"
        },
        "orders.PaymentMethod": {
            "verbose_name": "Payment Method",
            "verbose_name_plural": "Payment Methods",
            "group": "Order Management"
        },
        
        # Store & Content Management
        "store.Organization": {
            "verbose_name": "Store Information",
            "verbose_name_plural": "Store Information",
            "group": "Store Management"
        },
        "store.HeroSlide": {
            "verbose_name": "Homepage Slide",
            "verbose_name_plural": "Homepage Slides",
            "group": "Store Management"
        },
        "store.HeroSlideImage": {
            "verbose_name": "Slide Image",
            "verbose_name_plural": "Slide Images",
            "group": "Store Management"
        },
        "store.AboutPageContent": {
            "verbose_name": "About Page Content",
            "verbose_name_plural": "About Page Contents",
            "group": "Store Management"
        },
        "store.Testimonial": {
            "verbose_name": "Customer Testimonial",
            "verbose_name_plural": "Customer Testimonials",
            "group": "Store Management"
        },
    }
    
    @classmethod
    def apply_model_name_overrides(cls):
        """
        Apply custom verbose names to all models
        """
        for model_key, config in cls.MODEL_DISPLAY_NAMES.items():
            try:
                app_label, model_name = model_key.split('.')
                model = apps.get_model(app_label, model_name)
                
                # Override the model's Meta verbose names
                if hasattr(model._meta, 'verbose_name'):
                    model._meta.verbose_name = config['verbose_name']
                if hasattr(model._meta, 'verbose_name_plural'):
                    model._meta.verbose_name_plural = config['verbose_name_plural']
                    
            except (LookupError, ValueError) as e:
                print(f"Warning: Could not apply name override for {model_key}: {e}")
    
    @classmethod
    def get_models_by_group(cls):
        """
        Get models organized by their logical groups
        """
        groups = {}
        for model_key, config in cls.MODEL_DISPLAY_NAMES.items():
            group = config['group']
            if group not in groups:
                groups[group] = []
            groups[group].append({
                'model_key': model_key,
                'verbose_name': config['verbose_name'],
                'verbose_name_plural': config['verbose_name_plural']
            })
        return groups
    
    @classmethod
    def get_admin_menu_structure(cls):
        """
        Get the recommended admin menu structure
        """
        return {
            "User Management": {
                "icon": "fas fa-users-cog",
                "models": ["account.UserAccount"]
            },
            "Product Catalog": {
                "icon": "fas fa-shopping-bag", 
                "models": [
                    "products.ProductCategory",
                    "products.Product", 
                    "products.Variation",
                    "products.ReviewRating"
                ]
            },
            "Shopping Management": {
                "icon": "fas fa-shopping-basket",
                "models": ["carts.Cart", "carts.CartItem"]
            },
            "Order Management": {
                "icon": "fas fa-shopping-cart",
                "models": [
                    "orders.Order",
                    "orders.OrderProduct", 
                    "orders.Payment",
                    "orders.PaymentMethod"
                ]
            },
            "Store Management": {
                "icon": "fas fa-store",
                "models": [
                    "store.Organization",
                    "store.HeroSlide",
                    "store.HeroSlideImage", 
                    "store.AboutPageContent",
                    "store.Testimonial"
                ]
            }
        }


def setup_admin_customizations():
    """
    Setup function to apply all admin customizations
    Call this in your Django app's ready() method
    """
    ModelNameOverride.apply_model_name_overrides()


# Custom admin site configuration
def configure_admin_site():
    """
    Configure the admin site with custom titles and headers
    """
    from django.contrib import admin

    admin.site.site_header = "MahBeauty Administration"
    admin.site.site_title = "MahBeauty Admin"
    admin.site.index_title = "Welcome to MahBeauty Administration"
    admin.site.site_url = "/"  # Link to main site
    admin.site.enable_nav_sidebar = True


def get_jazzmin_model_names():
    """
    Get model names formatted for Jazzmin configuration
    """
    model_names = {}
    for model_key, config in ModelNameOverride.MODEL_DISPLAY_NAMES.items():
        model_names[model_key] = config['verbose_name_plural']
    return model_names


# Note: Don't auto-apply overrides on import to avoid AppRegistryNotReady errors
# These will be applied when Django apps are ready
