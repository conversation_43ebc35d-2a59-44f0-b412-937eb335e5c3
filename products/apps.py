from django.apps import AppConfig


class ProductConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "products"
    verbose_name = "Product Catalog"

    def ready(self):
        import products.signals

        # Apply custom model names when app is ready
        from products.models import ProductCategory, Product, Variation, ReviewRating

        ProductCategory._meta.verbose_name = "Product Category"
        ProductCategory._meta.verbose_name_plural = "Product Categories"

        Product._meta.verbose_name = "Beauty Product"
        Product._meta.verbose_name_plural = "Beauty Products"

        Variation._meta.verbose_name = "Product Variation"
        Variation._meta.verbose_name_plural = "Product Variations"

        ReviewRating._meta.verbose_name = "Customer Review"
        ReviewRating._meta.verbose_name_plural = "Customer Reviews"
